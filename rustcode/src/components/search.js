// Check if we're running in Tauri or web mode
const isTauri = typeof window !== 'undefined' && window.__TAURI_IPC__;

let invoke;
if (isTauri) {
    const tauriModule = await import('@tauri-apps/api/tauri');
    invoke = tauriModule.invoke;
} else {
    invoke = async (command, args) => {
        if (window.rustCodeApp && window.rustCodeApp.invoke) {
            return window.rustCodeApp.invoke(command, args);
        }
        console.log(`Search invoke: ${command}`, args);
        return null;
    };
}

export class SearchManager {
    constructor(container, onFileSelect) {
        this.container = container;
        this.onFileSelect = onFileSelect;
        this.searchResults = [];
        this.currentSearchId = 0;
        this.isSearching = false;
        
        this.init();
    }

    init() {
        this.createSearchUI();
        this.setupEventListeners();
    }

    createSearchUI() {
        this.container.innerHTML = `
            <div class="search-header">
                <div class="search-input-container">
                    <input type="text" id="search-query" placeholder="Search in files..." class="search-input">
                    <div class="search-input-actions">
                        <button id="search-regex-btn" class="search-toggle-btn" title="Use Regular Expression">.*</button>
                        <button id="search-case-btn" class="search-toggle-btn" title="Match Case">Aa</button>
                        <button id="search-word-btn" class="search-toggle-btn" title="Match Whole Word">Ab</button>
                    </div>
                </div>
                <div class="search-replace-container" style="display: none;">
                    <input type="text" id="replace-query" placeholder="Replace with..." class="search-input">
                    <div class="search-replace-actions">
                        <button id="replace-btn" class="search-action-btn" title="Replace">Replace</button>
                        <button id="replace-all-btn" class="search-action-btn" title="Replace All">Replace All</button>
                    </div>
                </div>
                <div class="search-controls">
                    <button id="search-btn" class="search-primary-btn">Search</button>
                    <button id="search-replace-toggle" class="search-toggle-btn" title="Toggle Replace">⇄</button>
                    <button id="search-close-btn" class="search-close-btn" title="Close">×</button>
                </div>
            </div>
            <div class="search-filters">
                <div class="search-filter-group">
                    <label>Include:</label>
                    <input type="text" id="search-include" placeholder="*.js, *.ts, *.css" class="search-filter-input">
                </div>
                <div class="search-filter-group">
                    <label>Exclude:</label>
                    <input type="text" id="search-exclude" placeholder="node_modules, .git" class="search-filter-input">
                </div>
            </div>
            <div class="search-status">
                <span id="search-status-text">Ready to search</span>
                <div id="search-progress" class="search-progress" style="display: none;">
                    <div class="search-progress-bar"></div>
                </div>
            </div>
            <div class="search-results" id="search-results">
                <div class="search-results-empty">
                    <p>No search results</p>
                    <p class="search-results-hint">Use Ctrl+Shift+F to search across files</p>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Search button
        document.getElementById('search-btn').addEventListener('click', () => {
            this.performSearch();
        });

        // Search input
        document.getElementById('search-query').addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // Replace input
        document.getElementById('replace-query').addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.performReplace();
            }
        });

        // Toggle buttons
        document.getElementById('search-regex-btn').addEventListener('click', (e) => {
            this.toggleSearchOption(e.target, 'regex');
        });

        document.getElementById('search-case-btn').addEventListener('click', (e) => {
            this.toggleSearchOption(e.target, 'case');
        });

        document.getElementById('search-word-btn').addEventListener('click', (e) => {
            this.toggleSearchOption(e.target, 'word');
        });

        // Replace toggle
        document.getElementById('search-replace-toggle').addEventListener('click', () => {
            this.toggleReplaceMode();
        });

        // Replace buttons
        document.getElementById('replace-btn').addEventListener('click', () => {
            this.performReplace();
        });

        document.getElementById('replace-all-btn').addEventListener('click', () => {
            this.performReplaceAll();
        });

        // Close button
        document.getElementById('search-close-btn').addEventListener('click', () => {
            this.hide();
        });
    }

    toggleSearchOption(button, option) {
        button.classList.toggle('active');
        button.dataset[option] = button.classList.contains('active');
    }

    toggleReplaceMode() {
        const replaceContainer = document.querySelector('.search-replace-container');
        const isVisible = replaceContainer.style.display !== 'none';
        
        replaceContainer.style.display = isVisible ? 'none' : 'block';
        
        if (!isVisible) {
            document.getElementById('replace-query').focus();
        }
    }

    async performSearch() {
        if (this.isSearching) return;

        const query = document.getElementById('search-query').value.trim();
        if (!query) return;

        this.isSearching = true;
        this.currentSearchId++;
        
        const searchOptions = {
            query: query,
            regex: document.getElementById('search-regex-btn').classList.contains('active'),
            caseSensitive: document.getElementById('search-case-btn').classList.contains('active'),
            wholeWord: document.getElementById('search-word-btn').classList.contains('active'),
            include: document.getElementById('search-include').value.trim(),
            exclude: document.getElementById('search-exclude').value.trim()
        };

        this.showProgress('Searching...');

        try {
            const results = await this.searchInFiles(searchOptions);
            this.displayResults(results, query);
        } catch (error) {
            console.error('Search failed:', error);
            this.showStatus('Search failed: ' + error.message, 'error');
        } finally {
            this.isSearching = false;
            this.hideProgress();
        }
    }

    async searchInFiles(options) {
        // Get all files in workspace
        const tree = await invoke('get_directory_tree', { path: '/' });
        const files = this.getAllFiles(tree);
        
        const results = [];
        let processedFiles = 0;

        for (const file of files) {
            if (this.shouldSkipFile(file.path, options)) {
                continue;
            }

            try {
                const content = await invoke('read_file_content', { path: file.path });
                const matches = this.findMatches(content, options, file.path);
                
                if (matches.length > 0) {
                    results.push({
                        file: file,
                        matches: matches,
                        totalMatches: matches.length
                    });
                }
            } catch (error) {
                console.warn(`Failed to search in file ${file.path}:`, error);
            }

            processedFiles++;
            this.updateProgress(processedFiles, files.length);
        }

        return results;
    }

    getAllFiles(node, files = []) {
        if (!node.is_directory) {
            files.push(node);
        } else if (node.children) {
            node.children.forEach(child => this.getAllFiles(child, files));
        }
        return files;
    }

    shouldSkipFile(filePath, options) {
        // Check include patterns
        if (options.include) {
            const includePatterns = options.include.split(',').map(p => p.trim());
            const matches = includePatterns.some(pattern => {
                const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                return regex.test(filePath);
            });
            if (!matches) return true;
        }

        // Check exclude patterns
        if (options.exclude) {
            const excludePatterns = options.exclude.split(',').map(p => p.trim());
            const matches = excludePatterns.some(pattern => {
                const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                return regex.test(filePath);
            });
            if (matches) return true;
        }

        return false;
    }

    findMatches(content, options, filePath) {
        const matches = [];
        const lines = content.split('\n');
        
        let searchRegex;
        try {
            if (options.regex) {
                const flags = options.caseSensitive ? 'g' : 'gi';
                searchRegex = new RegExp(options.query, flags);
            } else {
                let pattern = options.query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                if (options.wholeWord) {
                    pattern = `\\b${pattern}\\b`;
                }
                const flags = options.caseSensitive ? 'g' : 'gi';
                searchRegex = new RegExp(pattern, flags);
            }
        } catch (error) {
            throw new Error('Invalid regular expression: ' + error.message);
        }

        lines.forEach((line, lineIndex) => {
            let match;
            searchRegex.lastIndex = 0; // Reset regex state
            
            while ((match = searchRegex.exec(line)) !== null) {
                matches.push({
                    line: lineIndex + 1,
                    column: match.index + 1,
                    text: line,
                    match: match[0],
                    matchStart: match.index,
                    matchEnd: match.index + match[0].length
                });
                
                // Prevent infinite loop for zero-length matches
                if (match.index === searchRegex.lastIndex) {
                    searchRegex.lastIndex++;
                }
            }
        });

        return matches;
    }

    displayResults(results, query) {
        const resultsContainer = document.getElementById('search-results');
        
        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="search-results-empty">
                    <p>No results found for "${query}"</p>
                    <p class="search-results-hint">Try adjusting your search terms or filters</p>
                </div>
            `;
            this.showStatus('No results found');
            return;
        }

        const totalMatches = results.reduce((sum, result) => sum + result.totalMatches, 0);
        this.showStatus(`Found ${totalMatches} matches in ${results.length} files`);

        let html = '<div class="search-results-list">';
        
        results.forEach(result => {
            html += `
                <div class="search-result-file">
                    <div class="search-result-file-header" data-path="${result.file.path}">
                        <span class="search-result-file-icon">📄</span>
                        <span class="search-result-file-name">${result.file.name}</span>
                        <span class="search-result-file-path">${result.file.path}</span>
                        <span class="search-result-file-count">${result.totalMatches}</span>
                    </div>
                    <div class="search-result-matches">
            `;
            
            result.matches.forEach(match => {
                const beforeMatch = match.text.substring(0, match.matchStart);
                const matchText = match.text.substring(match.matchStart, match.matchEnd);
                const afterMatch = match.text.substring(match.matchEnd);
                
                html += `
                    <div class="search-result-match" data-path="${result.file.path}" data-line="${match.line}" data-column="${match.column}">
                        <span class="search-result-line-number">${match.line}</span>
                        <span class="search-result-line-text">
                            ${this.escapeHtml(beforeMatch)}<mark>${this.escapeHtml(matchText)}</mark>${this.escapeHtml(afterMatch)}
                        </span>
                    </div>
                `;
            });
            
            html += '</div></div>';
        });
        
        html += '</div>';
        resultsContainer.innerHTML = html;

        // Add click handlers
        resultsContainer.querySelectorAll('.search-result-match').forEach(element => {
            element.addEventListener('click', () => {
                const path = element.dataset.path;
                const line = parseInt(element.dataset.line);
                const column = parseInt(element.dataset.column);
                this.onFileSelect(path, line, column);
            });
        });

        resultsContainer.querySelectorAll('.search-result-file-header').forEach(element => {
            element.addEventListener('click', () => {
                const path = element.dataset.path;
                this.onFileSelect(path);
            });
        });
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    async performReplace() {
        // TODO: Implement single replace
        console.log('Perform replace');
    }

    async performReplaceAll() {
        // TODO: Implement replace all
        console.log('Perform replace all');
    }

    showProgress(message) {
        document.getElementById('search-status-text').textContent = message;
        document.getElementById('search-progress').style.display = 'block';
    }

    hideProgress() {
        document.getElementById('search-progress').style.display = 'none';
    }

    updateProgress(current, total) {
        const percentage = (current / total) * 100;
        const progressBar = document.querySelector('.search-progress-bar');
        progressBar.style.width = percentage + '%';
    }

    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('search-status-text');
        statusElement.textContent = message;
        statusElement.className = `search-status-${type}`;
    }

    show() {
        this.container.style.display = 'block';
        document.getElementById('search-query').focus();
    }

    hide() {
        this.container.style.display = 'none';
    }

    setQuery(query) {
        document.getElementById('search-query').value = query;
    }
}
