export class SettingsManager {
    constructor(container, onSettingsChange) {
        this.container = container;
        this.onSettingsChange = onSettingsChange;
        this.settings = this.loadSettings();
        
        this.init();
    }

    init() {
        this.createSettingsUI();
        this.setupEventListeners();
        this.applySettings();
    }

    loadSettings() {
        try {
            const stored = localStorage.getItem('rustcode-settings');
            if (stored) {
                return { ...this.getDefaultSettings(), ...JSON.parse(stored) };
            }
        } catch (error) {
            console.warn('Failed to load settings:', error);
        }
        return this.getDefaultSettings();
    }

    getDefaultSettings() {
        return {
            // Theme settings
            theme: 'dark',
            
            // Editor settings
            fontSize: 14,
            fontFamily: 'Consolas, Monaco, "Courier New", monospace',
            tabSize: 4,
            insertSpaces: true,
            wordWrap: 'on',
            lineNumbers: 'on',
            minimap: true,
            renderWhitespace: 'selection',
            renderIndentGuides: true,
            bracketPairColorization: true,
            
            // UI settings
            sidebarWidth: 250,
            terminalHeight: 300,
            searchPanelWidth: 350,
            
            // Behavior settings
            autoSave: true,
            autoSaveDelay: 1000,
            formatOnSave: false,
            trimTrailingWhitespace: false,
            
            // Terminal settings
            terminalFontSize: 14,
            terminalFontFamily: 'Consolas, Monaco, "Courier New", monospace',
            terminalCursorBlink: true,
            terminalScrollback: 1000
        };
    }

    saveSettings() {
        try {
            localStorage.setItem('rustcode-settings', JSON.stringify(this.settings));
        } catch (error) {
            console.warn('Failed to save settings:', error);
        }
    }

    createSettingsUI() {
        this.container.innerHTML = `
            <div class="settings-header">
                <h2>Settings</h2>
                <button id="settings-close-btn" class="settings-close-btn" title="Close">×</button>
            </div>
            <div class="settings-content">
                <div class="settings-section">
                    <h3>Appearance</h3>
                    <div class="settings-group">
                        <label for="theme-select">Theme:</label>
                        <select id="theme-select" class="settings-select">
                            <option value="dark">Dark</option>
                            <option value="light">Light</option>
                            <option value="high-contrast">High Contrast</option>
                        </select>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Editor</h3>
                    <div class="settings-group">
                        <label for="font-size-input">Font Size:</label>
                        <input type="number" id="font-size-input" class="settings-input" min="8" max="72" value="${this.settings.fontSize}">
                    </div>
                    <div class="settings-group">
                        <label for="font-family-input">Font Family:</label>
                        <input type="text" id="font-family-input" class="settings-input" value="${this.settings.fontFamily}">
                    </div>
                    <div class="settings-group">
                        <label for="tab-size-input">Tab Size:</label>
                        <input type="number" id="tab-size-input" class="settings-input" min="1" max="8" value="${this.settings.tabSize}">
                    </div>
                    <div class="settings-group">
                        <label for="word-wrap-select">Word Wrap:</label>
                        <select id="word-wrap-select" class="settings-select">
                            <option value="off">Off</option>
                            <option value="on">On</option>
                            <option value="wordWrapColumn">At Column</option>
                            <option value="bounded">Bounded</option>
                        </select>
                    </div>
                    <div class="settings-group">
                        <label class="settings-checkbox">
                            <input type="checkbox" id="insert-spaces-checkbox" ${this.settings.insertSpaces ? 'checked' : ''}>
                            Insert Spaces
                        </label>
                    </div>
                    <div class="settings-group">
                        <label class="settings-checkbox">
                            <input type="checkbox" id="minimap-checkbox" ${this.settings.minimap ? 'checked' : ''}>
                            Show Minimap
                        </label>
                    </div>
                    <div class="settings-group">
                        <label class="settings-checkbox">
                            <input type="checkbox" id="line-numbers-checkbox" ${this.settings.lineNumbers === 'on' ? 'checked' : ''}>
                            Show Line Numbers
                        </label>
                    </div>
                    <div class="settings-group">
                        <label class="settings-checkbox">
                            <input type="checkbox" id="render-whitespace-checkbox" ${this.settings.renderWhitespace !== 'none' ? 'checked' : ''}>
                            Render Whitespace
                        </label>
                    </div>
                    <div class="settings-group">
                        <label class="settings-checkbox">
                            <input type="checkbox" id="render-indent-guides-checkbox" ${this.settings.renderIndentGuides ? 'checked' : ''}>
                            Render Indent Guides
                        </label>
                    </div>
                    <div class="settings-group">
                        <label class="settings-checkbox">
                            <input type="checkbox" id="bracket-colorization-checkbox" ${this.settings.bracketPairColorization ? 'checked' : ''}>
                            Bracket Pair Colorization
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Behavior</h3>
                    <div class="settings-group">
                        <label class="settings-checkbox">
                            <input type="checkbox" id="auto-save-checkbox" ${this.settings.autoSave ? 'checked' : ''}>
                            Auto Save
                        </label>
                    </div>
                    <div class="settings-group">
                        <label for="auto-save-delay-input">Auto Save Delay (ms):</label>
                        <input type="number" id="auto-save-delay-input" class="settings-input" min="100" max="10000" step="100" value="${this.settings.autoSaveDelay}">
                    </div>
                    <div class="settings-group">
                        <label class="settings-checkbox">
                            <input type="checkbox" id="format-on-save-checkbox" ${this.settings.formatOnSave ? 'checked' : ''}>
                            Format on Save
                        </label>
                    </div>
                    <div class="settings-group">
                        <label class="settings-checkbox">
                            <input type="checkbox" id="trim-whitespace-checkbox" ${this.settings.trimTrailingWhitespace ? 'checked' : ''}>
                            Trim Trailing Whitespace
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Terminal</h3>
                    <div class="settings-group">
                        <label for="terminal-font-size-input">Font Size:</label>
                        <input type="number" id="terminal-font-size-input" class="settings-input" min="8" max="72" value="${this.settings.terminalFontSize}">
                    </div>
                    <div class="settings-group">
                        <label for="terminal-font-family-input">Font Family:</label>
                        <input type="text" id="terminal-font-family-input" class="settings-input" value="${this.settings.terminalFontFamily}">
                    </div>
                    <div class="settings-group">
                        <label class="settings-checkbox">
                            <input type="checkbox" id="terminal-cursor-blink-checkbox" ${this.settings.terminalCursorBlink ? 'checked' : ''}>
                            Cursor Blink
                        </label>
                    </div>
                    <div class="settings-group">
                        <label for="terminal-scrollback-input">Scrollback Lines:</label>
                        <input type="number" id="terminal-scrollback-input" class="settings-input" min="100" max="10000" step="100" value="${this.settings.terminalScrollback}">
                    </div>
                </div>

                <div class="settings-actions">
                    <button id="settings-reset-btn" class="settings-button settings-button-secondary">Reset to Defaults</button>
                    <button id="settings-apply-btn" class="settings-button settings-button-primary">Apply</button>
                </div>
            </div>
        `;

        // Set current values
        document.getElementById('theme-select').value = this.settings.theme;
        document.getElementById('word-wrap-select').value = this.settings.wordWrap;
    }

    setupEventListeners() {
        // Close button
        document.getElementById('settings-close-btn').addEventListener('click', () => {
            this.hide();
        });

        // Apply button
        document.getElementById('settings-apply-btn').addEventListener('click', () => {
            this.applyChanges();
        });

        // Reset button
        document.getElementById('settings-reset-btn').addEventListener('click', () => {
            this.resetToDefaults();
        });

        // Theme change
        document.getElementById('theme-select').addEventListener('change', (e) => {
            this.settings.theme = e.target.value;
            this.applyTheme();
            this.saveSettings();
        });

        // Real-time updates for some settings
        document.getElementById('font-size-input').addEventListener('input', (e) => {
            this.settings.fontSize = parseInt(e.target.value);
            this.applyEditorSettings();
            this.saveSettings();
        });
    }

    applyChanges() {
        // Collect all settings from form
        this.settings.fontSize = parseInt(document.getElementById('font-size-input').value);
        this.settings.fontFamily = document.getElementById('font-family-input').value;
        this.settings.tabSize = parseInt(document.getElementById('tab-size-input').value);
        this.settings.wordWrap = document.getElementById('word-wrap-select').value;
        this.settings.insertSpaces = document.getElementById('insert-spaces-checkbox').checked;
        this.settings.minimap = document.getElementById('minimap-checkbox').checked;
        this.settings.lineNumbers = document.getElementById('line-numbers-checkbox').checked ? 'on' : 'off';
        this.settings.renderWhitespace = document.getElementById('render-whitespace-checkbox').checked ? 'selection' : 'none';
        this.settings.renderIndentGuides = document.getElementById('render-indent-guides-checkbox').checked;
        this.settings.bracketPairColorization = document.getElementById('bracket-colorization-checkbox').checked;
        
        this.settings.autoSave = document.getElementById('auto-save-checkbox').checked;
        this.settings.autoSaveDelay = parseInt(document.getElementById('auto-save-delay-input').value);
        this.settings.formatOnSave = document.getElementById('format-on-save-checkbox').checked;
        this.settings.trimTrailingWhitespace = document.getElementById('trim-whitespace-checkbox').checked;
        
        this.settings.terminalFontSize = parseInt(document.getElementById('terminal-font-size-input').value);
        this.settings.terminalFontFamily = document.getElementById('terminal-font-family-input').value;
        this.settings.terminalCursorBlink = document.getElementById('terminal-cursor-blink-checkbox').checked;
        this.settings.terminalScrollback = parseInt(document.getElementById('terminal-scrollback-input').value);

        this.applySettings();
        this.saveSettings();
        this.onSettingsChange(this.settings);
    }

    resetToDefaults() {
        this.settings = this.getDefaultSettings();
        this.createSettingsUI();
        this.setupEventListeners();
        this.applySettings();
        this.saveSettings();
        this.onSettingsChange(this.settings);
    }

    applySettings() {
        this.applyTheme();
        this.applyEditorSettings();
        this.applyTerminalSettings();
    }

    applyTheme() {
        document.body.className = `theme-${this.settings.theme}`;
        
        // Apply Monaco theme
        if (window.monaco) {
            const monacoTheme = this.getMonacoTheme(this.settings.theme);
            window.monaco.editor.setTheme(monacoTheme);
        }
    }

    getMonacoTheme(theme) {
        switch (theme) {
            case 'light':
                return 'vs';
            case 'high-contrast':
                return 'hc-black';
            default:
                return 'rustcode-dark';
        }
    }

    applyEditorSettings() {
        if (window.rustCodeApp && window.rustCodeApp.editorManager && window.rustCodeApp.editorManager.editor) {
            const editor = window.rustCodeApp.editorManager.editor;
            editor.updateOptions({
                fontSize: this.settings.fontSize,
                fontFamily: this.settings.fontFamily,
                tabSize: this.settings.tabSize,
                insertSpaces: this.settings.insertSpaces,
                wordWrap: this.settings.wordWrap,
                lineNumbers: this.settings.lineNumbers,
                minimap: { enabled: this.settings.minimap },
                renderWhitespace: this.settings.renderWhitespace,
                renderIndentGuides: this.settings.renderIndentGuides,
                bracketPairColorization: { enabled: this.settings.bracketPairColorization }
            });
        }
    }

    applyTerminalSettings() {
        // Terminal settings will be applied when creating new terminals
        // Existing terminals would need to be recreated to apply new settings
    }

    show() {
        this.container.style.display = 'block';
    }

    hide() {
        this.container.style.display = 'none';
    }

    getSettings() {
        return this.settings;
    }

    updateSetting(key, value) {
        this.settings[key] = value;
        this.saveSettings();
        this.applySettings();
        this.onSettingsChange(this.settings);
    }
}
