/* File Explorer Styles */
#file-explorer {
    flex: 1;
    overflow-y: auto;
    padding: 5px 0;
}

.file-tree {
    list-style: none;
    margin: 0;
    padding: 0;
}

.file-tree-item {
    display: flex;
    align-items: center;
    padding: 2px 0;
    cursor: pointer;
    user-select: none;
    position: relative;
}

.file-tree-item:hover {
    background-color: #2a2d2e;
}

.file-tree-item.selected {
    background-color: #37373d;
}

.file-tree-item.active {
    background-color: #094771;
}

.file-tree-indent {
    width: 16px;
    height: 16px;
    display: inline-block;
}

.file-tree-arrow {
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #cccccc;
    cursor: pointer;
    transition: transform 0.1s ease;
}

.file-tree-arrow.expanded {
    transform: rotate(90deg);
}

.file-tree-arrow:hover {
    background-color: #3e3e42;
    border-radius: 2px;
}

.file-tree-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.file-tree-icon.folder {
    color: #dcb67a;
}

.file-tree-icon.folder.open {
    color: #dcb67a;
}

.file-tree-icon.file {
    color: #cccccc;
}

/* File type specific icons */
.file-tree-icon.file[data-ext="rs"] {
    color: #dea584;
}

.file-tree-icon.file[data-ext="js"],
.file-tree-icon.file[data-ext="mjs"] {
    color: #f7df1e;
}

.file-tree-icon.file[data-ext="ts"] {
    color: #3178c6;
}

.file-tree-icon.file[data-ext="py"] {
    color: #3776ab;
}

.file-tree-icon.file[data-ext="html"],
.file-tree-icon.file[data-ext="htm"] {
    color: #e34c26;
}

.file-tree-icon.file[data-ext="css"] {
    color: #1572b6;
}

.file-tree-icon.file[data-ext="json"] {
    color: #cbcb41;
}

.file-tree-icon.file[data-ext="md"] {
    color: #083fa1;
}

.file-tree-icon.file[data-ext="xml"] {
    color: #e37933;
}

.file-tree-icon.file[data-ext="yaml"],
.file-tree-icon.file[data-ext="yml"] {
    color: #cb171e;
}

.file-tree-icon.file[data-ext="toml"] {
    color: #9c4221;
}

.file-tree-icon.file[data-ext="sh"],
.file-tree-icon.file[data-ext="bash"] {
    color: #89e051;
}

.file-tree-label {
    flex: 1;
    font-size: 13px;
    color: #cccccc;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 5px;
}

.file-tree-item.modified .file-tree-label::after {
    content: " •";
    color: #f0f0f0;
    font-weight: bold;
}

.file-tree-children {
    margin-left: 16px;
}

.file-tree-children.collapsed {
    display: none;
}

/* Loading state */
.file-tree-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #888;
    font-style: italic;
}

/* Empty state */
.file-tree-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #888;
    font-style: italic;
    text-align: center;
}

/* Drag and drop */
.file-tree-item.drag-over {
    background-color: #094771;
    border: 1px dashed #007acc;
}

.file-tree-item.dragging {
    opacity: 0.5;
}

/* Context menu positioning */
.file-tree-item.context-menu-target {
    background-color: #37373d;
}

/* Drag and drop styles */
.file-tree-item.dragging {
    opacity: 0.5;
    background-color: #094771;
}

.file-tree-item.drag-over {
    background-color: #094771;
    border: 1px dashed #007acc;
    border-radius: 2px;
}

/* Rename input */
.file-tree-rename-input {
    background-color: #3c3c3c;
    border: 1px solid #007acc;
    color: #cccccc;
    font-size: 13px;
    font-family: inherit;
    padding: 1px 4px;
    margin: 0;
    outline: none;
    border-radius: 2px;
    width: 100%;
    min-width: 80px;
}

.file-tree-rename-input:focus {
    border-color: #007acc;
    box-shadow: 0 0 0 1px #007acc;
}

/* Enhanced context menu */
.context-menu-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.context-menu-separator {
    height: 1px;
    background-color: #3e3e42;
    margin: 4px 0;
}

/* File operation feedback */
.file-tree-item.cut {
    opacity: 0.6;
}

.file-tree-item.copied {
    background-color: #2d5a2d;
}
